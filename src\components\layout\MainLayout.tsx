"use client";

import { ReactNode } from "react";
import { Navbar } from "./Navbar";
import { useSession } from "next-auth/react";
import { FanPageMessageProvider } from "@/contexts/FanPageMessageContext";
import { FanPageMessageBox } from "@/components/fan-pages/FanPageMessageBox";

interface MainLayoutProps {
  children: ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { status } = useSession();
  const isAuthenticated = status === "authenticated";

  // Online status tracking disabled - Socket.IO removed
  // TODO: Implement alternative online status system

  return (
    <FanPageMessageProvider>
      <div className="flex min-h-screen flex-col bg-gray-50">
        {isAuthenticated && <Navbar />}
        <main className="flex-1 pt-16">{children}</main>
        {isAuthenticated && <FanPageMessageBox />}
      </div>
    </FanPageMessageProvider>
  );
}
